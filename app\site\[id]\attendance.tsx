import { ThemedText } from '@/components/ThemedText';
import { supabase } from '@/lib/supabase';
import { Feather, MaterialIcons } from '@expo/vector-icons';
import { Stack, useLocalSearchParams } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import {
  ActivityIndicator,
  Alert,
  FlatList,
  Linking,
  Modal,
  RefreshControl,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  useColorScheme,
  View
} from 'react-native';

// Types
type Laborer = {
  id: string;
  full_name: string;
  phone_number: string;
  category_id: string;
  category_name: string;
  remarks: string | null;
  is_active: boolean;
  daily_wage: number | null;
};

type AttendanceRecord = {
  id: string;
  laborer_id: string;
  status: 'present' | 'absent' | 'half_day' | 'overtime';
  attendance_date: string;
  overtime_hours?: number;
};

type LaborCategory = {
  id: string;
  name: string;
};

// Type for category with null option
type CategoryWithNull = LaborCategory | { id: null; name: string };

// Define styles type for the CompactWeekCalendar component
type CompactCalendarStyles = {
  compactCalendarContainer: any;
  calendarArrow: any;
  weekDaysContainer: any;
  dayContainer: any;
  selectedDayContainer: any;
  todayContainer: any;
  dayNameText: any;
  selectedDayText: any;
  dayCircle: any;
  selectedDayCircle: any;
  dayNumberText: any;
  selectedDayNumberText: any;
};

// Custom calendar style component
const CompactWeekCalendar = ({ 
  selectedDate, 
  onDateChange 
}: { 
  selectedDate: string, 
  onDateChange: (date: string) => void 
}) => {
  // Generate 7 days - 3 days before, current day, and 3 days after
  const generateWeekDays = () => {
    const today = new Date(selectedDate);
    const days = [];
    
    for (let i = -3; i <= 3; i++) {
      const date = new Date(today);
      date.setDate(today.getDate() + i);
      days.push({
        dateString: date.toISOString().split('T')[0],
        day: date.getDate(),
        month: date.getMonth(),
        year: date.getFullYear(),
        dayName: date.toLocaleDateString('en-US', { weekday: 'short' }),
      });
    }
    
    return days;
  };
  
  const weekDays = generateWeekDays();
  
  // Get styles cast to our type
  const calendarStyles = styles as unknown as CompactCalendarStyles;
  
  return (
    <View style={calendarStyles.compactCalendarContainer}>
      <TouchableOpacity style={calendarStyles.calendarArrow} onPress={() => {
        const prevDate = new Date(selectedDate);
        prevDate.setDate(prevDate.getDate() - 7);
        onDateChange(prevDate.toISOString().split('T')[0]);
      }}>
        <Feather name="chevron-left" size={24} color="#4A5568" />
      </TouchableOpacity>
      
      <View style={calendarStyles.weekDaysContainer}>
        {weekDays.map((day) => (
          <TouchableOpacity
            key={day.dateString}
            style={[
              calendarStyles.dayContainer,
              selectedDate === day.dateString && calendarStyles.selectedDayContainer,
              day.dateString === new Date().toISOString().split('T')[0] && calendarStyles.todayContainer
            ]}
            onPress={() => onDateChange(day.dateString)}
          >
            <ThemedText style={[
              calendarStyles.dayNameText,
              selectedDate === day.dateString && calendarStyles.selectedDayText
            ]}>
              {day.dayName}
            </ThemedText>
            <View style={[
              calendarStyles.dayCircle,
              selectedDate === day.dateString && calendarStyles.selectedDayCircle
            ]}>
              <ThemedText style={[
                calendarStyles.dayNumberText,
                selectedDate === day.dateString && calendarStyles.selectedDayNumberText
              ]}>
                {day.day}
              </ThemedText>
            </View>
          </TouchableOpacity>
        ))}
      </View>
      
      <TouchableOpacity style={calendarStyles.calendarArrow} onPress={() => {
        const nextDate = new Date(selectedDate);
        nextDate.setDate(nextDate.getDate() + 7);
        onDateChange(nextDate.toISOString().split('T')[0]);
      }}>
        <Feather name="chevron-right" size={24} color="#4A5568" />
      </TouchableOpacity>
    </View>
  );
};

export default function AttendanceScreen() {
  const params = useLocalSearchParams();
  const siteId = typeof params.id === 'string' ? params.id : Array.isArray(params.id) ? params.id[0] : '';
  const colorScheme = useColorScheme();
  
  // States
  const [loading, setLoading] = useState(true);
  const [initialLoad, setInitialLoad] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  const [laborers, setLaborers] = useState<Laborer[]>([]);
  const [attendance, setAttendance] = useState<Record<string, AttendanceRecord>>({});
  const [categories, setCategories] = useState<LaborCategory[]>([]);
  const [userRole, setUserRole] = useState<'Super Admin' | 'Admin' | 'Member' | null>(null);
  const [showAddLaborer, setShowAddLaborer] = useState(false);
  const [filteredLaborers, setFilteredLaborers] = useState<Laborer[]>([]);
  const [selectedCategoryFilter, setSelectedCategoryFilter] = useState<string | null>(null);
  const [showFilterModal, setShowFilterModal] = useState(false);
  const [showBulkActionModal, setShowBulkActionModal] = useState(false);
  const [bulkActionCategory, setBulkActionCategory] = useState<string | null>(null);
  
  // OT Modal related states
  const [showOTModal, setShowOTModal] = useState(false);
  const [overtimeHours, setOvertimeHours] = useState('');
  const [selectedLaborerId, setSelectedLaborerId] = useState<string | null>(null);
  const [calculatedOTPay, setCalculatedOTPay] = useState<number | null>(null);
  
  // Bulk OT Modal related states
  const [showBulkOTModal, setShowBulkOTModal] = useState(false);
  const [bulkOTHours, setBulkOTHours] = useState('');
  const [bulkOTLaborers, setBulkOTLaborers] = useState<Laborer[]>([]);
  
  // Form states for adding a new laborer
  const [newLaborerName, setNewLaborerName] = useState('');
  const [newLaborerPhone, setNewLaborerPhone] = useState('');
  const [newLaborerCategory, setNewLaborerCategory] = useState<string>('');
  const [newLaborerRemarks, setNewLaborerRemarks] = useState('');
  const [newLaborerDailyWage, setNewLaborerDailyWage] = useState('');
  const [showCategoryPicker, setShowCategoryPicker] = useState(false);
  const [selectedLaborer, setSelectedLaborer] = useState<Laborer | null>(null);
  const [showLaborerDetails, setShowLaborerDetails] = useState(false);

  // Add this to the state declarations
  const [newCategoryName, setNewCategoryName] = useState('');
  const [showAddCategory, setShowAddCategory] = useState(false);

  // Add state for error tracking
  const [error, setError] = useState<string | null>(null);

  // Check if the selected date is today
  const isToday = useMemo(() => {
    const today = new Date().toISOString().split('T')[0];
    return selectedDate === today;
  }, [selectedDate]);

  // Check if user can edit based on role and date
  const canEdit = useMemo(() => {
    if (userRole === 'Super Admin') return true;
    if (userRole === 'Admin' && isToday) return true;
    return false;
  }, [userRole, isToday]);

  // Add a function to validate database tables
  const validateDatabaseStructure = async () => {
    if (!siteId) return;
    
    try {
      // Check attendance table
      const { error: attendanceError } = await supabase
        .from('attendance')
        .select('id')
        .limit(1);
        
      if (attendanceError) {
        console.error('Attendance table error:', attendanceError);
        setError(`Database error: ${attendanceError.message}`);
        return false;
      }
      
      // Check laborers table
      const { error: laborersError } = await supabase
        .from('laborers')
        .select('id')
        .limit(1);
        
      if (laborersError) {
        console.error('Laborers table error:', laborersError);
        setError(`Database error: ${laborersError.message}`);
        return false;
      }
      
      return true;
    } catch (error) {
      console.error('Error validating database structure:', error);
      setError('Failed to validate database structure');
      return false;
    }
  };
  
  // Memoize the filtered laborers to improve performance
  const filteredLaborersList = useMemo(() => {
    if (selectedCategoryFilter) {
      return laborers.filter(l => l.category_id === selectedCategoryFilter);
    } else {
      return laborers;
    }
  }, [laborers, selectedCategoryFilter]);

  // Set filtered laborers in state after memoization calculation
  useEffect(() => {
    setFilteredLaborers(filteredLaborersList);
  }, [filteredLaborersList]);

  // Enhanced fetchData with optimizations
  const fetchData = async (isRefreshing = false) => {
    // Reset error state
    setError(null);
    
    if (!siteId) {
      console.log('Cannot fetch data: No site ID provided');
      setError('No site ID provided');
      setLoading(false);
      setRefreshing(false);
      setInitialLoad(false);
      return;
    }
    
    try {
      if (!isRefreshing) {
        setLoading(true);
      }
      
      // First validate database structure
      const isValid = await validateDatabaseStructure();
      if (!isValid) {
        setLoading(false);
        setRefreshing(false);
        setInitialLoad(false);
        return;
      }
      
      console.log(`Fetching data for site: ${siteId}`);
      
      // Get current user role for this site
      const { data: userData, error: userError } = await supabase.auth.getUser();
      if (userError) {
        console.error('Error getting user:', userError);
        Alert.alert('Error', 'Failed to authenticate user');
        setLoading(false);
        setRefreshing(false);
        setInitialLoad(false);
        return;
      }
      
      if (!userData?.user) {
        console.log('No authenticated user found');
        Alert.alert('Error', 'Please log in to view attendance');
        setLoading(false);
        setRefreshing(false);
        setInitialLoad(false);
        return;
      }
      
      console.log(`Authenticated as user: ${userData.user.id}`);
      
      // Promise.all to parallelize requests for better performance
      const [memberResponse, categoriesResponse] = await Promise.all([
        supabase
          .from('site_members')
          .select('role')
          .eq('site_id', siteId)
          .eq('user_id', userData.user.id)
          .single(),
        fetchCategories()
      ]);
      
      const { data: memberData, error: memberError } = memberResponse;
      
      if (memberError && memberError.code !== 'PGRST116') { // PGRST116 is "no rows returned"
        console.error('Error fetching member data:', memberError);
        Alert.alert('Error', 'Failed to verify site membership');
        setLoading(false);
        setRefreshing(false);
        setInitialLoad(false);
        return;
      }
      
      if (memberData) {
        console.log(`User role for site: ${memberData.role}`);
        setUserRole(memberData.role as 'Super Admin' | 'Admin' | 'Member');
      } else {
        console.log('User is not a member of this site');
        setUserRole(null);
      }
      
      // Fetch laborers based on selected date
      await fetchLaborersForDate();
      
      setLoading(false);
      setRefreshing(false);
      setInitialLoad(false);
    } catch (error) {
      console.error('Error in fetchData:', error);
      setError('An unexpected error occurred');
      setLoading(false);
      setRefreshing(false);
      setInitialLoad(false);
    }
  };

  // New function to fetch laborers based on the selected date
  const fetchLaborersForDate = async () => {
    if (!siteId) return;
    
    try {
      console.log('Fetching laborers for date:', selectedDate);
      
      const today = new Date().toISOString().split('T')[0];
      const isPastDate = selectedDate < today;
      const isFutureDate = selectedDate > today;
      
      // First, get all laborers for this site (including deleted ones for past dates)
      let laborersQuery = supabase
        .from('laborers')
        .select(`
          id,
          full_name,
          phone_number,
          category_id,
          remarks,
          is_active,
          daily_wage,
          created_at,
          deleted_at,
          labor_categories(name)
        `)
        .eq('site_id', siteId);
      
      // For current and future dates, exclude deleted laborers
      if (!isPastDate) {
        laborersQuery = laborersQuery.is('deleted_at', null);
      }
      
      const { data: laborersData, error: laborersError } = await laborersQuery.order('full_name');
      
      if (laborersError) {
        console.error('Error fetching laborers:', laborersError);
        Alert.alert('Error', 'Failed to load laborers');
        return;
      }
      
      if (laborersData && laborersData.length > 0) {
        console.log(`Found ${laborersData.length} laborers from database`);
        
        // Filter laborers based on the selected date
        const filteredLaborers = laborersData.filter(laborer => {
          const createdDate = new Date(laborer.created_at).toISOString().split('T')[0];
          const deletedDate = laborer.deleted_at ? new Date(laborer.deleted_at).toISOString().split('T')[0] : null;
          
          if (isPastDate) {
            // For past dates: Include laborers that existed on that date
            // They must have been created on or before the selected date
            // And either not deleted, or deleted after the selected date
            return createdDate <= selectedDate && (!deletedDate || deletedDate > selectedDate);
          } else if (isFutureDate) {
            // For future dates: Only include active laborers created on or before the selected date
            return !laborer.deleted_at && createdDate <= selectedDate;
          } else {
            // For today's date: Include all active laborers created today or before
            return !laborer.deleted_at;
          }
        });
        
        console.log(`Filtered to ${filteredLaborers.length} laborers for date ${selectedDate}`);
        
        const formattedLaborers = filteredLaborers.map(laborer => {
          // Handle the labor_categories relationship properly
          const categoryName = Array.isArray(laborer.labor_categories) 
            ? laborer.labor_categories[0]?.name 
            : (laborer.labor_categories as any)?.name;
          
          return {
            id: laborer.id,
            full_name: laborer.deleted_at && isPastDate ? 
              `${laborer.full_name} (Deleted)` : laborer.full_name,
            phone_number: laborer.phone_number,
            category_id: laborer.category_id,
            category_name: categoryName || 'Unknown',
            remarks: laborer.remarks,
            is_active: laborer.is_active && !laborer.deleted_at,
            daily_wage: laborer.daily_wage
          };
        });
        
        setLaborers(formattedLaborers);
        
        // Fetch attendance for the selected date
        fetchAttendanceForDate();
      } else {
        console.log(`No laborers found for date ${selectedDate}`);
        setLaborers([]);
        setAttendance({});
      }
    } catch (error) {
      console.error('Error in fetchLaborersForDate:', error);
      Alert.alert('Error', 'Failed to load laborers for selected date');
    }
  };

  // Simplified function to fetch categories - made more efficient
  const fetchCategories = async () => {
    try {
      console.log('Fetching categories...');
      
      // Use loading state for categories
      setCategories([]);
      
      const { data: categoriesData, error: categoriesError } = await supabase
        .from('labor_categories')
        .select('*')
        .order('name');
      
      if (categoriesError) {
        console.error('Error fetching categories:', categoriesError);
        Alert.alert('Error', 'Failed to load categories');
        return;
      }
      
      if (categoriesData && categoriesData.length > 0) {
        console.log(`Found ${categoriesData.length} categories`);
        setCategories(categoriesData);
      } else {
        console.log('No categories found');
      }
    } catch (error) {
      console.error('Error in fetchCategories:', error);
      Alert.alert('Error', 'An unexpected error occurred');
    }
  };

  // Handle refresh
  const onRefresh = useCallback(() => {
    setRefreshing(true);
    fetchData(true);
  }, [selectedDate, siteId]);

  // Initial data fetch - ensure we only run this once
  useEffect(() => {
    if (siteId && initialLoad) {
      // Load categories early so they're available when needed
      fetchCategories();
      fetchData();
    }
  }, [siteId, initialLoad]);

  // Fetch laborers and attendance when date changes
  useEffect(() => {
    if (!loading && siteId) {
      console.log(`Date changed to ${selectedDate}, fetching laborers and attendance data`);
      fetchLaborersForDate();
    }
  }, [selectedDate, siteId]);

  // Fetch attendance for selected date
  const fetchAttendanceForDate = async () => {
    if (!siteId) {
      console.log('Cannot fetch attendance: No site ID provided');
      return;
    }
    
    try {
      console.log(`Fetching attendance for date: ${selectedDate}`);
      
      // Get attendance records for the selected date
      const { data: attendanceData, error } = await supabase
        .from('attendance')
        .select('*')
        .eq('site_id', siteId)
        .eq('attendance_date', selectedDate);
      
      if (error) {
        console.error('Error fetching attendance data:', error);
        Alert.alert('Error', 'Failed to load attendance data');
        return;
      }
      
      let attendanceMap: Record<string, AttendanceRecord> = {};
      
      if (attendanceData && attendanceData.length > 0) {
        console.log(`Found ${attendanceData.length} attendance records for date ${selectedDate}`);
        
        // Create attendance map
        attendanceData.forEach(record => {
          attendanceMap[record.laborer_id] = record;
        });
      } else {
        console.log(`No attendance records found for date ${selectedDate}`);
      }
      
      setAttendance(attendanceMap);
    } catch (error) {
      console.error('Exception in fetchAttendanceForDate:', error);
      Alert.alert('Error', 'An unexpected error occurred while loading attendance data');
    }
  };

  // Handle attendance status change
  const handleAttendanceChange = async (laborerId: string, status: string) => {
    if (!canEdit || !siteId) return;
    
    // Show OT Modal if status is overtime
    if (status === 'overtime') {
      setSelectedLaborerId(laborerId);
      setOvertimeHours('');
      setCalculatedOTPay(null);
      setShowOTModal(true);
      return;
    }
    
    try {
      const { data: userData } = await supabase.auth.getUser();
      if (!userData.user) return;
      
      const existingRecord = attendance[laborerId];
      
      if (existingRecord) {
        // Update existing record
        const { error } = await supabase
          .from('attendance')
          .update({
            status,
            updated_at: new Date().toISOString(),
            marked_by: userData.user.id,
            overtime_hours: null // Reset overtime hours if status is not overtime
          })
          .eq('id', existingRecord.id);
        
        if (error) {
          console.error('Error updating attendance:', error);
          Alert.alert('Error', 'Failed to update attendance status');
          return;
        }
      } else {
        // Create new record
        const { error } = await supabase
          .from('attendance')
          .insert({
            laborer_id: laborerId,
            site_id: siteId,
            attendance_date: selectedDate,
            status,
            marked_by: userData.user.id
          });
        
        if (error) {
          console.error('Error creating attendance record:', error);
          Alert.alert('Error', 'Failed to create attendance record');
          return;
        }
      }
      
      // Update local state
      fetchAttendanceForDate();
      
    } catch (error) {
      console.error('Error marking attendance:', error);
      Alert.alert('Error', 'Failed to update attendance');
    }
  };

  // Handle OT confirmation
  const handleOTConfirm = async () => {
    if (!selectedLaborerId || !overtimeHours || !canEdit || !siteId) {
      Alert.alert('Error', 'Please enter overtime hours');
      return;
    }
    
    const hours = parseFloat(overtimeHours);
    if (isNaN(hours) || hours <= 0) {
      Alert.alert('Error', 'Please enter a valid number of hours');
      return;
    }
    
    try {
      const { data: userData } = await supabase.auth.getUser();
      if (!userData.user) return;
      
      const existingRecord = attendance[selectedLaborerId];
      
      if (existingRecord) {
        // Update existing record
        const { error } = await supabase
          .from('attendance')
          .update({
            status: 'overtime',
            overtime_hours: hours,
            updated_at: new Date().toISOString(),
            marked_by: userData.user.id
          })
          .eq('id', existingRecord.id);
        
        if (error) {
          console.error('Error updating attendance with OT:', error);
          Alert.alert('Error', 'Failed to update overtime status');
          return;
        }
      } else {
        // Create new record
        const { error } = await supabase
          .from('attendance')
          .insert({
            laborer_id: selectedLaborerId,
            site_id: siteId,
            attendance_date: selectedDate,
            status: 'overtime',
            overtime_hours: hours,
            marked_by: userData.user.id
          });
        
        if (error) {
          console.error('Error creating attendance record with OT:', error);
          Alert.alert('Error', 'Failed to create overtime record');
          return;
        }
      }
      
      // Close modal and update local state
      setShowOTModal(false);
      fetchAttendanceForDate();
      
    } catch (error) {
      console.error('Error marking overtime attendance:', error);
      Alert.alert('Error', 'Failed to update overtime attendance');
    }
  };

  // Calculate OT pay based on daily wage and hours
  const calculateOTPay = (hours: string) => {
    if (!selectedLaborerId || !hours) {
      setCalculatedOTPay(null);
      return;
    }
    
    const selectedLaborer = laborers.find(l => l.id === selectedLaborerId);
    if (!selectedLaborer || !selectedLaborer.daily_wage) {
      setCalculatedOTPay(null);
      return;
    }
    
    const parsedHours = parseFloat(hours);
    if (isNaN(parsedHours) || parsedHours <= 0) {
      setCalculatedOTPay(null);
      return;
    }
    
    // Calculate OT pay (assuming 1.5x hourly rate)
    // First convert daily wage to hourly rate (assuming 8-hour workday)
    const hourlyRate = selectedLaborer.daily_wage / 8;
    const otRate = hourlyRate * 1.5;
    const otPay = otRate * parsedHours;
    
    setCalculatedOTPay(otPay);
  };

  // OT Modal component
  const OTModal = () => {
    const selectedLaborer = selectedLaborerId ? laborers.find(l => l.id === selectedLaborerId) : null;
    
    return (
      <Modal
        visible={showOTModal}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowOTModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <ThemedText style={styles.modalTitle}>Overtime Entry</ThemedText>
              <TouchableOpacity onPress={() => setShowOTModal(false)}>
                <Feather name="x" size={24} color="#4A5568" />
              </TouchableOpacity>
            </View>
            
            {selectedLaborer && (
              <View style={styles.otContent}>
                <ThemedText style={styles.laborerNameLarge}>{selectedLaborer.full_name}</ThemedText>
                <ThemedText style={styles.categoryInfo}>{selectedLaborer.category_name}</ThemedText>
                
                {selectedLaborer.daily_wage ? (
                  <ThemedText style={styles.wageInfo}>Daily Wage: ₹{selectedLaborer.daily_wage.toFixed(2)}</ThemedText>
                ) : (
                  <ThemedText style={styles.wageWarning}>No daily wage set for this laborer</ThemedText>
                )}
                
                <View style={styles.inputContainer}>
                  <ThemedText style={styles.inputLabel}>Hours worked (overtime):</ThemedText>
                  <TextInput
                    style={styles.hoursInput}
                    keyboardType="numeric"
                    value={overtimeHours}
                    onChangeText={(text) => {
                      setOvertimeHours(text);
                      calculateOTPay(text);
                    }}
                    placeholder="Enter hours"
                    placeholderTextColor="#9CA3AF"
                  />
                </View>
                
                {calculatedOTPay !== null && (
                  <View style={styles.payContainer}>
                    <ThemedText style={styles.payLabel}>Calculated OT Pay:</ThemedText>
                    <ThemedText style={styles.payAmount}>₹{calculatedOTPay.toFixed(2)}</ThemedText>
                  </View>
                )}
                
                <TouchableOpacity
                  style={[
                    styles.confirmButton,
                    (!overtimeHours || parseFloat(overtimeHours) <= 0) && styles.disabledButton
                  ]}
                  onPress={handleOTConfirm}
                  disabled={!overtimeHours || parseFloat(overtimeHours) <= 0}
                >
                  <ThemedText style={styles.confirmButtonText}>Confirm Overtime</ThemedText>
                </TouchableOpacity>
              </View>
            )}
          </View>
        </View>
      </Modal>
    );
  };
  
  // Bulk OT Modal component
  const BulkOTModal = () => {
    return (
      <Modal
        visible={showBulkOTModal}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowBulkOTModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <ThemedText style={styles.modalTitle}>Bulk Overtime Entry</ThemedText>
              <TouchableOpacity onPress={() => setShowBulkOTModal(false)}>
                <Feather name="x" size={24} color="#4A5568" />
              </TouchableOpacity>
            </View>
            
            <View style={styles.otContent}>
              <ThemedText style={styles.bulkOTInfoText}>
                Set overtime hours for {bulkOTLaborers.length} laborers
              </ThemedText>
              
              <View style={styles.inputContainer}>
                <ThemedText style={styles.inputLabel}>Hours worked (overtime):</ThemedText>
                <TextInput
                  style={styles.hoursInput}
                  keyboardType="numeric"
                  value={bulkOTHours}
                  onChangeText={setBulkOTHours}
                  placeholder="Enter hours"
                  placeholderTextColor="#9CA3AF"
                />
              </View>
              
              <TouchableOpacity
                style={[
                  styles.confirmButton,
                  (!bulkOTHours || parseFloat(bulkOTHours) <= 0) && styles.disabledButton
                ]}
                onPress={handleBulkOTConfirm}
                disabled={!bulkOTHours || parseFloat(bulkOTHours) <= 0}
              >
                <ThemedText style={styles.confirmButtonText}>Apply to All</ThemedText>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    );
  };
  
  // Handle bulk OT confirmation
  const handleBulkOTConfirm = async () => {
    if (!bulkOTHours || !canEdit || !siteId) {
      Alert.alert('Error', 'Please enter overtime hours');
      return;
    }
    
    const hours = parseFloat(bulkOTHours);
    if (isNaN(hours) || hours <= 0) {
      Alert.alert('Error', 'Please enter a valid number of hours');
      return;
    }
    
    try {
      const { data: userData } = await supabase.auth.getUser();
      if (!userData.user) return;
      
      // For each laborer, update or create attendance record with overtime hours
      const promises = bulkOTLaborers.map(async (laborer) => {
        const existingRecord = attendance[laborer.id];
        
        if (existingRecord) {
          // Update existing record
          await supabase
            .from('attendance')
            .update({
              status: 'overtime',
              overtime_hours: hours,
              updated_at: new Date().toISOString(),
              marked_by: userData.user!.id
            })
            .eq('id', existingRecord.id);
        } else {
          // Create new record
          await supabase
            .from('attendance')
            .insert({
              laborer_id: laborer.id,
              site_id: siteId,
              attendance_date: selectedDate,
              status: 'overtime',
              overtime_hours: hours,
              marked_by: userData.user!.id
            });
        }
      });
      
      // Wait for all updates to complete
      await Promise.all(promises);
      
      // Close modal and refresh data
      setShowBulkOTModal(false);
      fetchAttendanceForDate();
      
      // Show success message
      Alert.alert('Success', `${bulkOTLaborers.length} laborers marked as overtime with ${hours} hours`);
    } catch (error) {
      console.error('Error in bulk overtime update:', error);
      Alert.alert('Error', 'Failed to update overtime attendance');
    }
  };

  // Handle laborer deletion
  const handleDeleteLaborer = async (laborerId: string) => {
    if (!canEdit) return;
    
    Alert.alert(
      'Delete Laborer',
      'Are you sure you want to delete this laborer? They will still appear in past attendance records.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              // Soft delete by setting deleted_at
              const { error } = await supabase
                .from('laborers')
                .update({
                  is_active: false,
                  deleted_at: new Date().toISOString()
                })
                .eq('id', laborerId);
              
              if (error) {
                console.error('Error deleting laborer:', error);
                Alert.alert('Error', 'Failed to delete laborer');
                return;
              }
              
              // Update local state
              setLaborers(laborers.filter(l => l.id !== laborerId));
              
            } catch (error) {
              console.error('Error:', error);
              Alert.alert('Error', 'Failed to delete laborer');
            }
          }
        }
      ]
    );
  };

  // Handle adding a new laborer
  const handleAddLaborer = async () => {
    if (!newLaborerName.trim()) {
      Alert.alert('Missing Information', 'Please enter laborer name');
      return;
    }
    
    if (!newLaborerPhone.trim()) {
      Alert.alert('Missing Information', 'Please enter phone number');
      return;
    }
    
    if (!newLaborerCategory) {
      Alert.alert('Missing Information', 'Please select a category');
      return;
    }
    
    try {
      const { data, error } = await supabase
        .from('laborers')
        .insert({
          site_id: siteId,
          full_name: newLaborerName.trim(),
          phone_number: newLaborerPhone.trim(),
          category_id: newLaborerCategory,
          remarks: newLaborerRemarks.trim() || null,
          daily_wage: newLaborerDailyWage ? parseFloat(newLaborerDailyWage) : 0
        })
        .select();
      
      if (error) {
        console.error('Error adding laborer:', error);
        Alert.alert('Error', 'Failed to add laborer');
        return;
      }
      
      // Reset form
      setNewLaborerName('');
      setNewLaborerPhone('');
      setNewLaborerCategory('');
      setNewLaborerRemarks('');
      setNewLaborerDailyWage('');
      setShowAddLaborer(false);
      
      // Refresh data
      fetchData(true);
      
    } catch (error) {
      console.error('Error:', error);
      Alert.alert('Error', 'Failed to add laborer');
    }
  };

  // Handle updating laborer details
  const handleUpdateLaborer = async () => {
    if (!selectedLaborer) return;
    
    try {
      // Validate input
      if (!selectedLaborer.full_name.trim()) {
        Alert.alert('Missing Information', 'Please enter laborer name');
        return;
      }
      
      if (!selectedLaborer.phone_number.trim()) {
        Alert.alert('Missing Information', 'Please enter phone number');
        return;
      }
      
      if (!selectedLaborer.category_id) {
        Alert.alert('Missing Information', 'Please select a category');
        return;
      }
      
      const { error } = await supabase
        .from('laborers')
        .update({
          full_name: selectedLaborer.full_name.trim(),
          phone_number: selectedLaborer.phone_number.trim(),
          category_id: selectedLaborer.category_id,
          remarks: selectedLaborer.remarks || null,
          daily_wage: selectedLaborer.daily_wage || 0
        })
        .eq('id', selectedLaborer.id);
      
      if (error) {
        console.error('Error updating laborer:', error);
        Alert.alert('Error', 'Failed to update laborer details. ' + error.message);
        return;
      }
      
      setShowLaborerDetails(false);
      Alert.alert('Success', 'Laborer details updated successfully');
      fetchData(true);
      
    } catch (error) {
      console.error('Error:', error);
      Alert.alert('Error', 'Failed to update laborer details');
    }
  };

  // Bulk action modal component
  const BulkActionModal = () => (
    <Modal
      visible={showBulkActionModal}
      animationType="slide"
      transparent={true}
      onRequestClose={() => setShowBulkActionModal(false)}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <View style={styles.modalHeader}>
            <ThemedText style={styles.modalTitle}>Mark Attendance for All</ThemedText>
            <TouchableOpacity onPress={() => setShowBulkActionModal(false)}>
              <Feather name="x" size={24} color="#4A5568" />
            </TouchableOpacity>
          </View>
          
          <View style={styles.bulkActionContent}>
            <ThemedText style={styles.bulkActionText}>
              Choose a category and status to mark all laborers in that category.
            </ThemedText>
            
            {/* Category selection */}
            <ThemedText style={styles.inputLabel}>Select Category</ThemedText>
            <TouchableOpacity 
              style={styles.pickerButton}
              onPress={() => {
                setShowBulkActionModal(false);
                setTimeout(() => {
                  setShowCategoryPicker(true);
                  // Set a callback for when category is picked
                  setCategoryPickerCallback((categoryId: string | null) => {
                    setBulkActionCategory(categoryId);
                    setShowBulkActionModal(true);
                  });
                }, 300);
              }}
            >
              <ThemedText style={styles.pickerButtonText}>
                {bulkActionCategory 
                  ? categories.find(c => c.id === bulkActionCategory)?.name 
                  : 'All Categories'
                }
              </ThemedText>
              <Feather name="chevron-down" size={20} color="#4A5568" />
            </TouchableOpacity>
            
            <ThemedText style={[styles.inputLabel, {marginTop: 20}]}>
              Select Status to Apply
            </ThemedText>
            
            <View style={styles.bulkActionButtons}>
              <TouchableOpacity 
                style={styles.bulkStatusButton}
                onPress={() => handleBulkAttendanceChange('present')}
              >
                <View style={[styles.statusDot, {backgroundColor: '#10b981'}]} />
                <ThemedText style={styles.bulkStatusText}>Present</ThemedText>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={styles.bulkStatusButton}
                onPress={() => handleBulkAttendanceChange('absent')}
              >
                <View style={[styles.statusDot, {backgroundColor: '#ef4444'}]} />
                <ThemedText style={styles.bulkStatusText}>Absent</ThemedText>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={styles.bulkStatusButton}
                onPress={() => handleBulkAttendanceChange('half_day')}
              >
                <View style={[styles.statusDot, {backgroundColor: '#f59e0b'}]} />
                <ThemedText style={styles.bulkStatusText}>Half-day</ThemedText>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={styles.bulkStatusButton}
                onPress={() => handleBulkAttendanceChange('overtime')}
              >
                <View style={[styles.statusDot, {backgroundColor: '#6366f1'}]} />
                <ThemedText style={styles.bulkStatusText}>Overtime</ThemedText>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </View>
    </Modal>
  );

  // Handle bulk attendance marking
  const handleBulkAttendanceChange = async (status: string) => {
    if (!canEdit) return;
    
    try {
      // Get laborers to update
      const laborersToUpdate = bulkActionCategory 
        ? laborers.filter(l => l.category_id === bulkActionCategory)
        : laborers;
      
      if (laborersToUpdate.length === 0) {
        Alert.alert('No Laborers', 'No laborers match the selected criteria');
        return;
      }
      
      // For overtime, we need to collect hours for each laborer
      if (status === 'overtime') {
        // Close the bulk action modal and open the overtime hours input
        setShowBulkActionModal(false);
        // Store laborers to apply overtime to
        setBulkOTLaborers(laborersToUpdate);
        // Show the bulk OT hours input modal
        setShowBulkOTModal(true);
        return;
      }
      
      // Ask for confirmation for non-overtime statuses
      Alert.alert(
        'Confirm Bulk Action',
        `Mark ${laborersToUpdate.length} laborers as ${status}?`,
        [
          { text: 'Cancel', style: 'cancel' },
          { 
            text: 'Confirm', 
            onPress: async () => {
              setShowBulkActionModal(false);
              
              const { data: userData } = await supabase.auth.getUser();
              if (!userData.user) return;
              
              // For each laborer, update or create attendance record
              const promises = laborersToUpdate.map(async (laborer) => {
                const existingRecord = attendance[laborer.id];
                
                if (existingRecord) {
                  // Update existing record
                  await supabase
                    .from('attendance')
                    .update({
                      status,
                      updated_at: new Date().toISOString(),
                      marked_by: userData.user!.id,
                      overtime_hours: null // Reset overtime hours if status is not overtime
                    })
                    .eq('id', existingRecord.id);
                } else {
                  // Create new record
                  await supabase
                    .from('attendance')
                    .insert({
                      laborer_id: laborer.id,
                      site_id: siteId,
                      attendance_date: selectedDate,
                      status,
                      marked_by: userData.user!.id
                    });
                }
              });
              
              // Wait for all updates to complete
              await Promise.all(promises);
              
              // Refresh attendance data
              fetchAttendanceForDate();
              
              // Show success message
              Alert.alert('Success', `${laborersToUpdate.length} laborers marked as ${status}`);
            }
          }
        ]
      );
    } catch (error) {
      console.error('Error in bulk attendance update:', error);
      Alert.alert('Error', 'Failed to update attendance');
    }
  };

  // State to handle category picker callback
  const [categoryPickerCallback, setCategoryPickerCallback] = 
    useState<((categoryId: string | null) => void) | null>(null);

  // Add this function to handle adding a new category
  const handleAddCategory = async () => {
    if (!newCategoryName.trim()) {
      Alert.alert('Missing Information', 'Please enter a category name');
      return;
    }
    
    try {
      const { data, error } = await supabase
        .from('labor_categories')
        .insert({
          name: newCategoryName.trim()
        })
        .select();
      
      if (error) {
        console.error('Error adding category:', error);
        Alert.alert('Error', 'Failed to add category');
        return;
      }
      
      // Reset form and refresh categories
      setNewCategoryName('');
      setShowAddCategory(false);
      fetchCategories();
      
    } catch (error) {
      console.error('Error:', error);
      Alert.alert('Error', 'Failed to add category');
    }
  };

  // Simplified Category Picker Modal
  const CategoryPickerModal = () => {
    // Load categories once when the modal opens
    useEffect(() => {
      if (showCategoryPicker && categories.length === 0) {
        fetchCategories();
      }
    }, [showCategoryPicker]);
    
    // Simplified function to handle category selection
    const handleCategorySelect = (categoryId: string | null, categoryName?: string) => {
      console.log('Category selected:', categoryId, categoryName);
      
      if (selectedLaborer && categoryId) {
        console.log('Updating selected laborer category');
        // Update selected laborer's category
        setSelectedLaborer({...selectedLaborer, category_id: categoryId, category_name: categoryName || ''});
      } else if (categoryId) {
        console.log('Setting new laborer category');
        // Set category for new laborer
        setNewLaborerCategory(categoryId);
      } else if (categoryPickerCallback) {
        console.log('Using category picker callback');
        // Handle callback for filter/bulk actions
        categoryPickerCallback(categoryId);
        setCategoryPickerCallback(null);
      }
      
      // Close the picker
      setShowCategoryPicker(false);
      setShowAddCategory(false);
    };
    
    return (
      <Modal
        visible={showCategoryPicker}
        animationType="slide"
        transparent={true}
        onRequestClose={() => {
          setShowCategoryPicker(false);
          setCategoryPickerCallback(null);
          setShowAddCategory(false);
        }}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <ThemedText style={styles.modalTitle}>Select Category</ThemedText>
              <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                {!showAddCategory && (
                  <TouchableOpacity 
                    style={{ padding: 8, marginRight: 8 }}
                    onPress={() => setShowAddCategory(true)}
                  >
                    <Feather name="plus" size={20} color="#4A5568" />
                  </TouchableOpacity>
                )}
                <TouchableOpacity 
                  style={{ padding: 8, marginRight: 8 }}
                  onPress={fetchCategories}
                >
                  <Feather name="refresh-cw" size={20} color="#4A5568" />
                </TouchableOpacity>
                <TouchableOpacity onPress={() => {
                  setShowCategoryPicker(false);
                  setCategoryPickerCallback(null);
                  setShowAddCategory(false);
                }}>
                  <Feather name="x" size={24} color="#4A5568" />
                </TouchableOpacity>
              </View>
            </View>
            
            {showAddCategory ? (
              <View style={styles.formContainer}>
                <ThemedText style={styles.inputLabel}>Category Name</ThemedText>
                <TextInput
                  style={styles.textInput}
                  value={newCategoryName}
                  onChangeText={setNewCategoryName}
                  placeholder="Enter category name"
                  placeholderTextColor="#A0AEC0"
                  autoFocus
                />
                
                <View style={styles.modalFooter}>
                  <TouchableOpacity 
                    style={styles.cancelButton}
                    onPress={() => setShowAddCategory(false)}
                  >
                    <ThemedText style={styles.cancelButtonText}>Cancel</ThemedText>
                  </TouchableOpacity>
                  <TouchableOpacity 
                    style={styles.saveButton}
                    onPress={handleAddCategory}
                  >
                    <ThemedText style={styles.saveButtonText}>Save</ThemedText>
                  </TouchableOpacity>
                </View>
              </View>
            ) : categories.length === 0 ? (
              <View style={styles.emptyCategoriesContainer}>
                <ActivityIndicator size="large" color="#f97316" />
                <ThemedText style={styles.emptyCategoriesText}>
                  Loading categories...
                </ThemedText>
              </View>
            ) : (
              <FlatList
                data={[{id: null, name: 'All Categories'} as CategoryWithNull, ...categories]}
                renderItem={({ item }: { item: CategoryWithNull }) => (
                  <TouchableOpacity 
                    style={styles.categoryItem}
                    onPress={() => handleCategorySelect(item.id, item.name)}
                  >
                    <ThemedText style={styles.categoryItemText}>{item.name}</ThemedText>
                    {((newLaborerCategory === item.id && !categoryPickerCallback && !selectedLaborer && item.id) ||
                      (selectedLaborer && selectedLaborer.category_id === item.id && item.id)) && (
                      <Feather name="check" size={20} color="#10b981" />
                    )}
                  </TouchableOpacity>
                )}
                keyExtractor={(item: CategoryWithNull) => item.id?.toString() || 'all'}
                contentContainerStyle={styles.categoryList}
              />
            )}
          </View>
        </View>
      </Modal>
    );
  };

  // Filter category modal component
  const FilterCategoryModal = () => (
    <Modal
      visible={showFilterModal}
      animationType="slide"
      transparent={true}
      onRequestClose={() => setShowFilterModal(false)}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <View style={styles.modalHeader}>
            <ThemedText style={styles.modalTitle}>Filter by Category</ThemedText>
            <TouchableOpacity onPress={() => setShowFilterModal(false)}>
              <Feather name="x" size={24} color="#4A5568" />
            </TouchableOpacity>
          </View>
          
          <ScrollView style={styles.formContainer}>
            <TouchableOpacity 
              style={[
                styles.categoryItem, 
                !selectedCategoryFilter && styles.selectedCategoryItem
              ]}
              onPress={() => {
                setSelectedCategoryFilter(null);
                setShowFilterModal(false);
              }}
            >
              <ThemedText style={styles.categoryItemText}>All Categories</ThemedText>
              {!selectedCategoryFilter && (
                <Feather name="check" size={20} color="#10b981" />
              )}
            </TouchableOpacity>
            
            {categories.map(category => (
              <TouchableOpacity 
                key={category.id}
                style={[
                  styles.categoryItem, 
                  selectedCategoryFilter === category.id && styles.selectedCategoryItem
                ]}
                onPress={() => {
                  setSelectedCategoryFilter(category.id);
                  setShowFilterModal(false);
                }}
              >
                <ThemedText style={styles.categoryItemText}>{category.name}</ThemedText>
                {selectedCategoryFilter === category.id && (
                  <Feather name="check" size={20} color="#10b981" />
                )}
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
      </View>
    </Modal>
  );

  // Add button for Add Labor modal
  const renderAddLaborButton = () => {
    if (userRole !== 'Admin' && userRole !== 'Super Admin') return null;
    
    return (
      <TouchableOpacity 
        style={styles.addButton}
        onPress={() => {
          setNewLaborerName('');
          setNewLaborerPhone('');
          setNewLaborerCategory('');
          setNewLaborerRemarks('');
          setNewLaborerDailyWage('');
          setShowAddLaborer(true);
        }}
      >
        <Feather name="plus" size={18} color="white" />
        <ThemedText style={styles.addButtonText}>Add Labor</ThemedText>
      </TouchableOpacity>
    );
  };

  return (
    <>
      <Stack.Screen 
        options={{
          title: 'Attendance',
          headerShown: true,
          headerBackTitle: 'Back',
          headerRight: () => (
            <TouchableOpacity 
              style={{ padding: 8 }}
              onPress={onRefresh}
            >
              <Feather name="refresh-cw" size={20} color="#4A5568" />
            </TouchableOpacity>
          ),
        }}
      />
      <StatusBar style={colorScheme === 'dark' ? 'light' : 'dark'} />
      
      {initialLoad ? (
        // Show a clean fullscreen loader during initial load to prevent flickering
        <View style={{flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: 'white'}}>
          <ActivityIndicator size="large" color="#f97316" />
          <ThemedText style={{marginTop: 16, fontSize: 16}}>Loading attendance data...</ThemedText>
        </View>
      ) : (
        <View style={styles.container}>
          {/* Replace the horizontal calendar with our compact calendar */}
          <View style={styles.calendarContainer}>
            <CompactWeekCalendar
              selectedDate={selectedDate}
              onDateChange={(date) => setSelectedDate(date)}
            />
          </View>
          
          {/* Render the rest of the UI only if we're not in loading state */}
          {loading ? (
            <View style={styles.loadingContainer}>
              <View style={styles.skeletonHeader} />
              <View style={styles.skeletonStats} />
              <FlatList
                data={[1,2,3,4,5]}
                renderItem={() => (
                  <View style={styles.skeletonItem}>
                    <View style={styles.skeletonText} />
                    <View style={styles.skeletonButtons} />
                  </View>
                )}
                keyExtractor={item => `skeleton-${item}`}
                contentContainerStyle={styles.listContent}
              />
            </View>
          ) : error ? (
            <View style={styles.loadingContainer}>
              <MaterialIcons name="error-outline" size={60} color="#ef4444" />
              <ThemedText style={[styles.loadingText, { color: '#ef4444', marginTop: 16 }]}>
                {error}
              </ThemedText>
              <TouchableOpacity 
                style={{
                  backgroundColor: '#f97316',
                  paddingHorizontal: 16,
                  paddingVertical: 8,
                  borderRadius: 6,
                  marginTop: 16
                }}
                onPress={() => fetchData(true)}
              >
                <ThemedText style={{ color: 'white', fontWeight: '600' }}>Reload Data</ThemedText>
              </TouchableOpacity>
            </View>
          ) : (
            <>
              {/* Header with Add Button and Filter */}
              <View style={styles.header}>
                <ThemedText style={styles.dateTitle}>
                  {new Date(selectedDate).toLocaleDateString('en-US', { 
                    weekday: 'long', 
                    year: 'numeric', 
                    month: 'long', 
                    day: 'numeric' 
                  })}
                </ThemedText>
                
                <View style={styles.headerButtons}>
                  {canEdit && (
                    <TouchableOpacity 
                      style={styles.bulkActionButton}
                      onPress={() => setShowBulkActionModal(true)}
                    >
                      <MaterialIcons name="playlist-add-check" size={18} color="#4A5568" />
                      <ThemedText style={styles.bulkActionButtonText}>Bulk</ThemedText>
                    </TouchableOpacity>
                  )}
                  
                  <TouchableOpacity 
                    style={styles.filterButton}
                    onPress={() => setShowFilterModal(true)}
                  >
                    <Feather name="filter" size={18} color="#4A5568" />
                    <ThemedText style={styles.filterButtonText}>
                      {selectedCategoryFilter 
                        ? categories.find(c => c.id === selectedCategoryFilter)?.name 
                        : 'All'
                      }
                    </ThemedText>
                  </TouchableOpacity>
                  
                  {renderAddLaborButton()}
                </View>
              </View>
              
              {/* Stats Summary */}
              {filteredLaborers.length > 0 && (
                <View style={styles.statsSummary}>
                  <View style={styles.statItem}>
                    <ThemedText style={styles.statNumber}>{filteredLaborers.length}</ThemedText>
                    <ThemedText style={styles.statLabel}>Laborers</ThemedText>
                  </View>
                  
                  <View style={styles.statItem}>
                    <ThemedText style={styles.statNumber}>
                      {Object.values(attendance).filter(a => 
                        a.status === 'present' && 
                        filteredLaborers.some(l => l.id === a.laborer_id)
                      ).length}
                    </ThemedText>
                    <ThemedText style={styles.statLabel}>Present</ThemedText>
                  </View>
                  
                  <View style={styles.statItem}>
                    <ThemedText style={styles.statNumber}>
                      {Object.values(attendance).filter(a => 
                        a.status === 'absent' && 
                        filteredLaborers.some(l => l.id === a.laborer_id)
                      ).length}
                    </ThemedText>
                    <ThemedText style={styles.statLabel}>Absent</ThemedText>
                  </View>
                  
                  <View style={styles.statItem}>
                    <ThemedText style={styles.statNumber}>
                      {filteredLaborers.reduce((sum, laborer) => {
                        const record = attendance[laborer.id];
                        const status = record?.status;
                        const dailyWage = laborer.daily_wage || 0;
                        
                        if (status === 'present') {
                          return sum + dailyWage;
                        } else if (status === 'half_day') {
                          return sum + (dailyWage / 2);
                        } else if (status === 'overtime') {
                          // Calculate regular wage + overtime pay
                          // Overtime pay = (daily wage / 8) * 1.5 * overtime_hours
                          const regularWage = dailyWage;
                          const overtimeHours = record?.overtime_hours || 0;
                          const hourlyRate = dailyWage / 8;
                          const overtimePay = hourlyRate * 1.5 * overtimeHours;
                          return sum + regularWage + overtimePay;
                        }
                        return sum;
                      }, 0).toFixed(0)}
                    </ThemedText>
                    <ThemedText style={styles.statLabel}>Wage (₹)</ThemedText>
                  </View>
                </View>
              )}
              
              {/* Laborers List with Improved Performance */}
              {filteredLaborers.length === 0 ? (
                <View style={styles.emptyContainer}>
                  <MaterialIcons name="person-search" size={60} color="#CBD5E0" />
                  <ThemedText style={styles.emptyText}>
                    {laborers.length === 0 
                      ? "No laborers added yet" 
                      : "No laborers match the selected filter"
                    }
                  </ThemedText>
                  {laborers.length === 0 && (
                    <ThemedText style={{ fontSize: 14, color: '#6b7280', textAlign: 'center', marginBottom: 16 }}>
                      {userRole === 'Admin' || userRole === 'Super Admin' 
                        ? "Click the 'Add Labor' button to add laborers" 
                        : "Contact an administrator to add laborers to this site"
                      }
                    </ThemedText>
                  )}
                  {(userRole === 'Admin' || userRole === 'Super Admin') && laborers.length === 0 && (
                    <TouchableOpacity 
                      style={styles.emptyAddButton}
                      onPress={() => setShowAddLaborer(true)}
                    >
                      <ThemedText style={styles.emptyAddButtonText}>Add Labor</ThemedText>
                    </TouchableOpacity>
                  )}
                </View>
              ) : (
                <FlatList
                  data={filteredLaborers}
                  refreshControl={
                    <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
                  }
                  renderItem={({ item }) => (
                    <LaborerItem 
                      laborer={item}
                      attendanceStatus={attendance[item.id]?.status}
                      onStatusChange={canEdit ? (status) => handleAttendanceChange(item.id, status) : undefined}
                      onDelete={canEdit ? () => handleDeleteLaborer(item.id) : undefined}
                      onPress={() => {
                        setSelectedLaborer(item);
                        setShowLaborerDetails(true);
                      }}
                    />
                  )}
                  keyExtractor={item => item.id}
                  contentContainerStyle={styles.listContent}
                  maxToRenderPerBatch={10}
                  windowSize={10}
                  removeClippedSubviews={true}
                  initialNumToRender={10}
                />
              )}
            </>
          )}
          
          {/* Add Laborer Modal */}
          <Modal
            visible={showAddLaborer}
            animationType="fade"
            transparent={true}
            onRequestClose={() => setShowAddLaborer(false)}
          >
            <View style={styles.modalOverlay}>
              <View style={[
                styles.modalContent,
                { backgroundColor: colorScheme === 'dark' ? '#1f2937' : 'white' }
              ]}>
                <View style={styles.modalHeader}>
                  <ThemedText style={styles.modalTitle}>Add Labor</ThemedText>
                  <TouchableOpacity onPress={() => setShowAddLaborer(false)}>
                    <Feather name="x" size={24} color="#4A5568" />
                  </TouchableOpacity>
                </View>

                <ScrollView style={styles.formContainer}>
                  <ThemedText style={styles.inputLabel}>Full Name *</ThemedText>
                  <TextInput
                    style={[
                      styles.textInput,
                      {
                        backgroundColor: colorScheme === 'dark' ? '#374151' : '#F7FAFC',
                        color: colorScheme === 'dark' ? '#ffffff' : '#1f2937',
                        borderColor: colorScheme === 'dark' ? '#4b5563' : '#E2E8F0',
                      }
                    ]}
                    value={newLaborerName}
                    onChangeText={setNewLaborerName}
                    placeholder="Enter laborer's full name"
                    placeholderTextColor="#A0AEC0"
                  />

                  <ThemedText style={styles.inputLabel}>Phone Number *</ThemedText>
                  <TextInput
                    style={[
                      styles.textInput,
                      {
                        backgroundColor: colorScheme === 'dark' ? '#374151' : '#F7FAFC',
                        color: colorScheme === 'dark' ? '#ffffff' : '#1f2937',
                        borderColor: colorScheme === 'dark' ? '#4b5563' : '#E2E8F0',
                      }
                    ]}
                    value={newLaborerPhone}
                    onChangeText={setNewLaborerPhone}
                    placeholder="Enter phone number"
                    placeholderTextColor="#A0AEC0"
                    keyboardType="phone-pad"
                  />

                  <ThemedText style={styles.inputLabel}>Category *</ThemedText>
                  <TouchableOpacity
                    style={styles.pickerButton}
                    onPress={() => {
                      // Simplified approach - load categories directly when opening the picker
                      fetchCategories().then(() => {
                        setShowCategoryPicker(true);
                      });
                    }}
                  >
                    <ThemedText style={styles.pickerButtonText}>
                      {newLaborerCategory
                        ? categories.find(c => c.id === newLaborerCategory)?.name || 'Select Category'
                        : 'Select Category'
                      }
                    </ThemedText>
                    <Feather name="chevron-down" size={20} color="#4A5568" />
                  </TouchableOpacity>

                  <ThemedText style={styles.inputLabel}>Daily Wage</ThemedText>
                  <TextInput
                    style={[
                      styles.textInput,
                      {
                        backgroundColor: colorScheme === 'dark' ? '#374151' : '#F7FAFC',
                        color: colorScheme === 'dark' ? '#ffffff' : '#1f2937',
                        borderColor: colorScheme === 'dark' ? '#4b5563' : '#E2E8F0',
                      }
                    ]}
                    value={newLaborerDailyWage}
                    onChangeText={setNewLaborerDailyWage}
                    placeholder="Enter daily wage amount"
                    placeholderTextColor="#A0AEC0"
                    keyboardType="numeric"
                  />

                  <ThemedText style={styles.inputLabel}>Remarks (Optional)</ThemedText>
                  <TextInput
                    style={[
                      styles.textInput,
                      styles.remarksInput,
                      {
                        backgroundColor: colorScheme === 'dark' ? '#374151' : '#F7FAFC',
                        color: colorScheme === 'dark' ? '#ffffff' : '#1f2937',
                        borderColor: colorScheme === 'dark' ? '#4b5563' : '#E2E8F0',
                      }
                    ]}
                    value={newLaborerRemarks}
                    onChangeText={setNewLaborerRemarks}
                    placeholder="Enter any additional remarks"
                    placeholderTextColor="#A0AEC0"
                    multiline
                    numberOfLines={3}
                    textAlignVertical="top"
                  />
                </ScrollView>
                
                <View style={styles.modalFooter}>
                  <TouchableOpacity 
                    style={styles.cancelButton}
                    onPress={() => setShowAddLaborer(false)}
                  >
                    <ThemedText style={styles.cancelButtonText}>Cancel</ThemedText>
                  </TouchableOpacity>
                  <TouchableOpacity 
                    style={styles.saveButton}
                    onPress={handleAddLaborer}
                  >
                    <ThemedText style={styles.saveButtonText}>Save</ThemedText>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          </Modal>
          
          {/* Show Bulk Action Modal */}
          <BulkActionModal />
          
          {/* Show Filter Category Modal */}
          <FilterCategoryModal />
          
          {/* Category Picker Modal */}
          <CategoryPickerModal />
          
          {/* Laborer Details Bottom Sheet */}
          <Modal
            visible={showLaborerDetails}
            animationType="fade"
            transparent={true}
            onRequestClose={() => setShowLaborerDetails(false)}
          >
            <View style={styles.bottomSheetOverlay}>
              <TouchableOpacity 
                style={styles.bottomSheetBackdrop}
                onPress={() => setShowLaborerDetails(false)}
              />
              <View style={styles.bottomSheetContent}>
                <View style={styles.bottomSheetHandle} />
                
                {selectedLaborer && (
                  <>
                    <ScrollView style={styles.formContainer}>
                      <ThemedText style={styles.inputLabel}>Full Name</ThemedText>
                      {canEdit ? (
                        <TextInput
                          style={styles.textInput}
                          value={selectedLaborer.full_name}
                          onChangeText={(text) => setSelectedLaborer({...selectedLaborer, full_name: text})}
                          editable={canEdit}
                        />
                      ) : (
                        <Text style={[styles.detailText, {color: 'black'}]}>{selectedLaborer.full_name}</Text>
                      )}
                      
                      <ThemedText style={styles.inputLabel}>Phone Number</ThemedText>
                      {canEdit ? (
                        <TextInput
                          style={styles.textInput}
                          value={selectedLaborer.phone_number}
                          onChangeText={(text) => setSelectedLaborer({...selectedLaborer, phone_number: text})}
                          keyboardType="phone-pad"
                          editable={canEdit}
                        />
                      ) : (
                        <Text style={[styles.detailText, {color: 'black'}]}>{selectedLaborer.phone_number}</Text>
                      )}
                      
                      <ThemedText style={styles.inputLabel}>Category</ThemedText>
                      {canEdit ? (
                        <TouchableOpacity 
                          style={styles.pickerButton}
                          onPress={() => {
                            setShowCategoryPicker(true);
                            setNewLaborerCategory(selectedLaborer.category_id);
                          }}
                          disabled={!canEdit}
                        >
                          <ThemedText style={styles.pickerButtonText}>
                            {selectedLaborer.category_name}
                          </ThemedText>
                          <Feather name="chevron-down" size={20} color="#4A5568" />
                        </TouchableOpacity>
                      ) : (
                        <Text style={[styles.detailText, {color: 'black'}]}>{selectedLaborer.category_name}</Text>
                      )}
                      
                      <ThemedText style={styles.inputLabel}>Daily Wage</ThemedText>
                      {canEdit ? (
                        <TextInput
                          style={styles.textInput}
                          value={selectedLaborer.daily_wage?.toString() || '0'}
                          onChangeText={(text) => setSelectedLaborer({...selectedLaborer, daily_wage: text ? parseFloat(text) : 0})}
                          keyboardType="numeric"
                          editable={canEdit}
                        />
                      ) : (
                        <Text style={[styles.detailText, {color: 'black'}]}>{selectedLaborer.daily_wage || '0'}</Text>
                      )}
                      
                      <ThemedText style={styles.inputLabel}>Remarks</ThemedText>
                      {canEdit ? (
                        <TextInput
                          style={[styles.textInput, styles.remarksInput]}
                          value={selectedLaborer.remarks || ''}
                          onChangeText={(text) => setSelectedLaborer({...selectedLaborer, remarks: text})}
                          multiline
                          numberOfLines={3}
                          textAlignVertical="top"
                          editable={canEdit}
                        />
                      ) : (
                        <Text style={[styles.detailText, {color: 'black'}]}>{selectedLaborer.remarks || 'N/A'}</Text>
                      )}
                      
                      <ThemedText style={styles.sectionTitle}>Attendance on {new Date(selectedDate).toLocaleDateString()}</ThemedText>
                      <View style={styles.attendanceHistoryItem}>
                        <ThemedText style={styles.attendanceHistoryLabel}>Status:</ThemedText>
                        <View style={[styles.statusIndicator, { 
                          backgroundColor: getStatusColor(attendance[selectedLaborer.id]?.status),
                          alignSelf: 'flex-start'
                        }]}>
                          <ThemedText style={styles.statusIndicatorText}>
                            {getStatusText(attendance[selectedLaborer.id]?.status)}
                          </ThemedText>
                        </View>
                      </View>
                    </ScrollView>
                    
                    {canEdit && (
                      <View style={styles.bottomSheetFooter}>
                        <TouchableOpacity 
                          style={styles.cancelButton}
                          onPress={() => setShowLaborerDetails(false)}
                        >
                          <ThemedText style={styles.cancelButtonText}>Cancel</ThemedText>
                        </TouchableOpacity>
                        <TouchableOpacity 
                          style={styles.saveButton}
                          onPress={handleUpdateLaborer}
                        >
                          <ThemedText style={styles.saveButtonText}>Save</ThemedText>
                        </TouchableOpacity>
                      </View>
                    )}
                  </>
                )}
              </View>
            </View>
          </Modal>
          
          {/* OT Modal */}
          <OTModal />
          
          {/* Bulk OT Modal */}
          <BulkOTModal />
        </View>
      )}
    </>
  );
}

// Laborer Item Component
function LaborerItem({ 
  laborer, 
  attendanceStatus, 
  onStatusChange, 
  onDelete,
  onPress 
}: { 
  laborer: Laborer; 
  attendanceStatus?: string; 
  onStatusChange?: (status: string) => void; 
  onDelete?: () => void;
  onPress: () => void;
}) {
  // Handle phone call
  const handlePhoneCall = () => {
    if (laborer.phone_number) {
      const phoneUrl = `tel:${laborer.phone_number}`;
      Linking.canOpenURL(phoneUrl)
        .then(supported => {
          if (supported) {
            Linking.openURL(phoneUrl);
          } else {
            Alert.alert('Error', 'Phone call functionality is not available on this device');
          }
        })
        .catch(err => {
          console.error('Error opening phone app:', err);
          Alert.alert('Error', 'Could not open phone app');
        });
    } else {
      Alert.alert('Error', 'No phone number available for this laborer');
    }
  };

  const getStatusColor = (status?: string) => {
    switch(status) {
      case 'present': return '#10b981'; // green
      case 'absent': return '#ef4444'; // red
      case 'half_day': return '#f59e0b'; // amber
      case 'overtime': return '#6366f1'; // indigo
      default: return '#CBD5E0'; // gray
    }
  };

  const getStatusText = (status?: string) => {
    switch(status) {
      case 'present': return 'P';
      case 'absent': return 'A';
      case 'half_day': return 'H';
      case 'overtime': return 'OT';
      default: return '-';
    }
  };

  // Memoize status color for better performance
  const statusColor = React.useMemo(() => getStatusColor(attendanceStatus), [attendanceStatus]);
  const statusText = React.useMemo(() => getStatusText(attendanceStatus), [attendanceStatus]);
  const colorScheme = useColorScheme();

  return (
    <TouchableOpacity 
      style={styles.laborerItem} 
      onPress={onPress}
      activeOpacity={0.7}
    >
      <View style={styles.laborerInfo}>
        <Text style={[styles.laborerName, {color: 'black'}]}>{laborer.full_name}</Text>
        <View style={styles.laborerSubInfo}>
          <ThemedText style={styles.laborerCategory}>{laborer.category_name}</ThemedText>
          {laborer.phone_number && (
            <TouchableOpacity 
              style={styles.phoneButton}
              onPress={handlePhoneCall}
              activeOpacity={0.7}
            >
              <Feather name="phone" size={16} color="#4A5568" />
            </TouchableOpacity>
          )}
        </View>
      </View>
      
      <View style={styles.statusButtons}>
        {onStatusChange ? (
          <>
            <TouchableOpacity 
              style={[styles.statusButton, { backgroundColor: attendanceStatus === 'present' ? '#10b981' : '#E2E8F0' }]}
              onPress={() => onStatusChange('present')}
              activeOpacity={0.7}
            >
              <ThemedText style={[styles.statusButtonText, { color: attendanceStatus === 'present' ? 'white' : '#4A5568' }]}>P</ThemedText>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={[styles.statusButton, { backgroundColor: attendanceStatus === 'absent' ? '#ef4444' : '#E2E8F0' }]}
              onPress={() => onStatusChange('absent')}
              activeOpacity={0.7}
            >
              <ThemedText style={[styles.statusButtonText, { color: attendanceStatus === 'absent' ? 'white' : '#4A5568' }]}>A</ThemedText>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={[styles.statusButton, { backgroundColor: attendanceStatus === 'half_day' ? '#f59e0b' : '#E2E8F0' }]}
              onPress={() => onStatusChange('half_day')}
              activeOpacity={0.7}
            >
              <ThemedText style={[styles.statusButtonText, { color: attendanceStatus === 'half_day' ? 'white' : '#4A5568' }]}>H</ThemedText>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={[styles.statusButton, { backgroundColor: attendanceStatus === 'overtime' ? '#6366f1' : '#E2E8F0' }]}
              onPress={() => onStatusChange('overtime')}
              activeOpacity={0.7}
            >
              <ThemedText style={[styles.statusButtonText, { color: attendanceStatus === 'overtime' ? 'white' : '#4A5568' }]}>OT</ThemedText>
            </TouchableOpacity>
          </>
        ) : (
          <View style={[styles.statusIndicator, { backgroundColor: statusColor }]}>
            <ThemedText style={styles.statusIndicatorText}>{statusText}</ThemedText>
          </View>
        )}
        
        {onDelete && (
          <TouchableOpacity 
            style={styles.deleteButton} 
            onPress={onDelete}
            activeOpacity={0.7}
          >
            <Feather name="trash-2" size={20} color="#ef4444" />
          </TouchableOpacity>
        )}
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  calendarContainer: {
    backgroundColor: 'white',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 1,
    elevation: 2,
    height: 120, // Adjusted for compact calendar
    borderBottomWidth: 1,
    borderBottomColor: '#E2E8F0',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#E2E8F0',
  },
  dateTitle: {
    fontSize: 16,
    fontWeight: '600',
    flex: 1,
  },
  headerButtons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  filterButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F1F5F9',
    borderRadius: 6,
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginRight: 8,
  },
  filterButtonText: {
    color: '#4A5568',
    fontWeight: '500',
    fontSize: 14,
    marginLeft: 4,
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f97316',
    borderRadius: 6,
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  addButtonText: {
    color: 'white',
    fontWeight: '600',
    marginLeft: 4,
  },
  listContent: {
    paddingHorizontal: 16,
    paddingBottom: 20,
  },
  laborerItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: 'white',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    marginTop: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 1,
    elevation: 1,
  },
  laborerInfo: {
    flex: 1,
  },
  laborerName: {
    fontSize: 16,
    fontWeight: '500',
  },
  laborerSubInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 2,
  },
  laborerCategory: {
    fontSize: 14,
    color: '#6b7280',
  },
  phoneButton: {
    marginLeft: 12,
    backgroundColor: '#f1f5f9',
    borderRadius: 4,
    padding: 5,
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusButtons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusButton: {
    width: 34,
    height: 34,
    borderRadius: 4,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 6,
  },
  statusButtonText: {
    fontWeight: '600',
    fontSize: 14,
  },
  statusIndicator: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 4,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 6,
  },
  statusIndicatorText: {
    color: 'white',
    fontWeight: '600',
    fontSize: 14,
  },
  deleteButton: {
    marginLeft: 12,
    padding: 5,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    fontSize: 16,
    color: '#6b7280',
    marginTop: 16,
    marginBottom: 20,
  },
  emptyAddButton: {
    backgroundColor: '#f97316',
    borderRadius: 6,
    paddingHorizontal: 20,
    paddingVertical: 10,
  },
  emptyAddButtonText: {
    color: 'white',
    fontWeight: '600',
    fontSize: 16,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContent: {
    backgroundColor: 'white',
    borderRadius: 12,
    width: '100%',
    maxWidth: 500,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E2E8F0',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  otContent: {
    paddingVertical: 10,
  },
  laborerNameLarge: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 8,
  },
  categoryInfo: {
    fontSize: 16,
    color: '#6b7280',
    marginBottom: 8,
  },
  wageInfo: {
    fontSize: 16,
    fontWeight: '500',
    color: '#10b981',
    marginBottom: 16,
  },
  wageWarning: {
    fontSize: 16,
    color: '#f59e0b',
    marginBottom: 16,
  },
  inputContainer: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 16,
    marginBottom: 8,
    fontWeight: '500',
  },
  hoursInput: {
    backgroundColor: '#F1F5F9',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    borderWidth: 1,
    borderColor: '#E2E8F0',
  },
  payContainer: {
    backgroundColor: '#F1F5F9',
    padding: 16,
    borderRadius: 8,
    marginBottom: 20,
  },
  payLabel: {
    fontSize: 16,
    color: '#4b5563',
    marginBottom: 4,
  },
  payAmount: {
    fontSize: 22,
    fontWeight: '700',
    color: '#10b981',
  },
  bulkOTInfoText: {
    fontSize: 16,
    color: '#4b5563',
    marginBottom: 16,
    textAlign: 'center',
  },
  confirmButton: {
    backgroundColor: '#6366f1',
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
    marginTop: 10,
  },
  disabledButton: {
    backgroundColor: '#CBD5E0',
  },
  confirmButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  formContainer: {
    padding: 16,
  },
  textInput: {
    backgroundColor: '#F7FAFC',
    borderWidth: 1,
    borderColor: '#E2E8F0',
    borderRadius: 6,
    padding: 12,
    fontSize: 16,
    marginBottom: 4,
  },
  remarksInput: {
    height: 100,
    paddingTop: 12,
  },
  modalFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingTop: 8,
  },
  pickerButton: {
    backgroundColor: '#F7FAFC',
    borderWidth: 1,
    borderColor: '#E2E8F0',
    borderRadius: 6,
    padding: 12,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  pickerButtonText: {
    fontSize: 16,
  },
  saveButton: {
    backgroundColor: '#f97316',
    borderRadius: 6,
    paddingHorizontal: 20,
    paddingVertical: 12,
    flex: 1,
    marginLeft: 8,
    alignItems: 'center',
  },
  saveButtonText: {
    color: 'white',
    fontWeight: '600',
    fontSize: 16,
  },
  cancelButton: {
    borderWidth: 1,
    borderColor: '#E2E8F0',
    borderRadius: 6,
    paddingHorizontal: 20,
    paddingVertical: 12,
    flex: 1,
    marginRight: 8,
    alignItems: 'center',
  },
  cancelButtonText: {
    color: '#4A5568',
    fontWeight: '600',
    fontSize: 16,
  },
  categoryItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E2E8F0',
  },
  categoryItemText: {
    fontSize: 16,
  },
  bottomSheetOverlay: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  bottomSheetBackdrop: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  bottomSheetContent: {
    backgroundColor: 'white',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingBottom: 24,
    maxHeight: '75%',
  },
  bottomSheetHandle: {
    width: 40,
    height: 5,
    borderRadius: 3,
    backgroundColor: '#CBD5E0',
    alignSelf: 'center',
    marginVertical: 10,
  },
  detailText: {
    fontSize: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E2E8F0',
    marginBottom: 4,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: 24,
    marginBottom: 8,
  },
  attendanceHistoryItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 12,
    backgroundColor: '#F7FAFC',
    borderRadius: 6,
    marginBottom: 8,
  },
  attendanceHistoryLabel: {
    fontSize: 16,
    fontWeight: '500',
  },
  bottomSheetFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#E2E8F0',
  },
  emptyCategoriesContainer: {
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyCategoriesText: {
    marginTop: 10,
    fontSize: 16,
    color: '#6b7280',
  },
  reloadButton: {
    backgroundColor: '#f97316',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
    marginTop: 16,
  },
  reloadButtonText: {
    color: 'white',
    fontWeight: '600',
  },
  categoryList: {
    paddingBottom: 16,
  },
  statsSummary: {
    flexDirection: 'row',
    backgroundColor: 'white',
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginBottom: 8,
    justifyContent: 'space-between',
    borderBottomWidth: 1,
    borderBottomColor: '#E2E8F0',
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 18,
    fontWeight: '700',
    color: '#f97316',
  },
  statLabel: {
    fontSize: 12,
    color: '#6b7280',
    marginTop: 2,
  },
  selectedCategoryItem: {
    backgroundColor: '#F0FDF4',
  },
  bulkActionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F1F5F9',
    borderRadius: 6,
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginRight: 8,
  },
  bulkActionButtonText: {
    color: '#4A5568',
    fontWeight: '500',
    fontSize: 14,
    marginLeft: 4,
  },
  bulkActionContent: {
    padding: 16,
  },
  bulkActionText: {
    fontSize: 16,
    color: '#6b7280',
    marginBottom: 16,
  },
  bulkActionButtons: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 8,
  },
  bulkStatusButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F1F5F9',
    borderRadius: 6,
    paddingHorizontal: 12,
    paddingVertical: 10,
    marginRight: 8,
    marginBottom: 8,
    width: '47%',
  },
  bulkStatusText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#4A5568',
    marginLeft: 8,
  },
  statusDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  skeletonCalendar: {
    height: 300,
    backgroundColor: '#E2E8F0',
    borderRadius: 6,
    opacity: 0.7,
    margin: 10,
  },
  skeletonHeader: {
    height: 50,
    backgroundColor: '#E2E8F0',
    opacity: 0.7,
    marginBottom: 10,
  },
  skeletonStats: {
    height: 60,
    backgroundColor: '#E2E8F0',
    opacity: 0.7,
    marginBottom: 10,
    marginHorizontal: 16,
    borderRadius: 6,
  },
  skeletonItem: {
    height: 70,
    backgroundColor: '#E2E8F0',
    opacity: 0.7,
    marginTop: 12,
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
  },
  skeletonText: {
    height: 40,
    width: 150,
    backgroundColor: '#CBD5E0',
    borderRadius: 4,
  },
  skeletonButtons: {
    height: 30,
    width: 120,
    backgroundColor: '#CBD5E0',
    borderRadius: 4,
  },
  compactCalendarContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 10,
    paddingHorizontal: 5,
    backgroundColor: 'white',
  },
  weekDaysContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    flex: 1,
  },
  calendarArrow: {
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  dayContainer: {
    alignItems: 'center',
    padding: 5,
    width: 42,
  },
  dayNameText: {
    fontSize: 12,
    color: '#4A5568',
    fontWeight: '500',
    marginBottom: 4,
  },
  dayCircle: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#f1f5f9',
    alignItems: 'center',
    justifyContent: 'center',
  },
  dayNumberText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#4A5568',
  },
  selectedDayContainer: {
    backgroundColor: '#fff8eb',
    borderRadius: 8,
  },
  selectedDayCircle: {
    backgroundColor: '#f97316',
  },
  selectedDayText: {
    color: '#f97316',
    fontWeight: 'bold',
  },
  selectedDayNumberText: {
    color: 'white',
    fontWeight: 'bold',
  },
  todayContainer: {
    borderRadius: 8,
  },
});

// Helper function to get status color
const getStatusColor = (status?: string) => {
  switch(status) {
    case 'present': return '#10b981'; // green
    case 'absent': return '#ef4444'; // red
    case 'half_day': return '#f59e0b'; // amber
    case 'overtime': return '#6366f1'; // indigo
    default: return '#CBD5E0'; // gray
  }
};

// Helper function to get status text
const getStatusText = (status?: string) => {
  switch(status) {
    case 'present': return 'P';
    case 'absent': return 'A';
    case 'half_day': return 'H';
    case 'overtime': return 'OT';
    default: return '-';
  }
}; 
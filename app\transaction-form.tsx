import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { supabase } from '@/lib/supabase';
import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import { router, useLocalSearchParams } from 'expo-router';
import React, { useEffect, useState } from 'react';
import {
    ActivityIndicator,
    Alert,
    FlatList,
    KeyboardAvoidingView,
    Modal,
    Platform,
    ScrollView,
    StyleSheet,
    TextInput,
    TouchableOpacity,
    View
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

interface Profile {
  id: string;
  full_name: string;
  email?: string;
  phone_number?: string;
}

interface Site {
  id: string;
  name: string;
  organization_name: string;
}

export default function TransactionFormScreen() {
  const insets = useSafeAreaInsets();
  const colorScheme = useColorScheme();
  const primaryColor = Colors[colorScheme ?? 'light'].primary;
  const { siteId, transactionId, mode } = useLocalSearchParams();

  const isEditMode = mode === 'edit' && transactionId;
  
  const [site, setSite] = useState<Site | null>(null);
  const [profiles, setProfiles] = useState<Profile[]>([]);
  const [filteredProfiles, setFilteredProfiles] = useState<Profile[]>([]);
  const [loading, setLoading] = useState(false);
  const [savingTransaction, setSavingTransaction] = useState(false);
  const [originalTransaction, setOriginalTransaction] = useState<any>(null);

  const [type, setType] = useState<'income' | 'expense'>('expense');
  const [amount, setAmount] = useState('');
  const [description, setDescription] = useState('');
  const [dateStr, setDateStr] = useState(new Date().toISOString().split('T')[0]);
  
  const [selectedProfile, setSelectedProfile] = useState<Profile | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [showProfileSearch, setShowProfileSearch] = useState(false);
  const [customName, setCustomName] = useState('');

  useEffect(() => {
    fetchProfiles();
    if (siteId) {
      fetchSiteDetails();
    }
    if (isEditMode) {
      loadTransactionForEdit();
    }
  }, [siteId, isEditMode]);

  const fetchSiteDetails = async () => {
    if (!siteId) return;
    
    try {
      const { data, error } = await supabase
        .from('sites')
        .select('id, name, organization_name')
        .eq('id', siteId)
        .single();

      if (error) throw error;
      setSite(data);
    } catch (error) {
      console.error('Error fetching site details:', error);
    }
  };

  const fetchProfiles = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('profiles')
        .select('id, full_name, email, phone_number');

      if (error) throw error;
      
      setProfiles(data || []);
    } catch (error) {
      console.error('Error fetching profiles:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (text: string) => {
    setSearchQuery(text);
    if (text.trim() === '') {
      setFilteredProfiles([]);
      return;
    }
    
    const filtered = profiles.filter(profile => 
      (profile.full_name && profile.full_name.toLowerCase().includes(text.toLowerCase())) ||
      (profile.email && profile.email.toLowerCase().includes(text.toLowerCase())) ||
      (profile.phone_number && profile.phone_number.includes(text))
    );
    
    setFilteredProfiles(filtered);
  };

  const selectProfile = (profile: Profile) => {
    setSelectedProfile(profile);
    setShowProfileSearch(false);
    setCustomName('');
  };

  const formatDateDisplay = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toDateString();
    } catch (error) {
      return dateString;
    }
  };

  const loadTransactionForEdit = async () => {
    if (!transactionId) return;

    try {
      setLoading(true);

      const { data, error } = await supabase
        .from('transactions')
        .select('*')
        .eq('id', transactionId)
        .single();

      if (error) {
        console.error('Error loading transaction for edit:', error);
        Alert.alert('Error', 'Failed to load transaction details');
        router.back();
        return;
      }

      // Populate form fields with existing transaction data
      setOriginalTransaction(data);
      setType(data.type);
      setAmount(data.amount.toString());
      setDescription(data.description);
      setDateStr(data.transaction_date.split('T')[0]); // Extract date part

      // If there's a profile_id, find and select the profile
      if (data.profile_id) {
        const profile = profiles.find(p => p.id === data.profile_id);
        if (profile) {
          setSelectedProfile(profile);
        }
      } else if (data.paid_to_received_from) {
        // If no profile but has paid_to_received_from, set as custom name
        setCustomName(data.paid_to_received_from);
      }

    } catch (error) {
      console.error('Error in loadTransactionForEdit:', error);
      Alert.alert('Error', 'Failed to load transaction details');
      router.back();
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    if (!amount || parseFloat(amount) <= 0) {
      Alert.alert('Error', 'Please enter a valid amount');
      return;
    }

    if (!description) {
      Alert.alert('Error', 'Please enter a description');
      return;
    }

    // Validate date format
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    if (!dateRegex.test(dateStr)) {
      Alert.alert('Error', 'Please enter a valid date in YYYY-MM-DD format');
      return;
    }

    const paidToReceivedFrom = selectedProfile ? selectedProfile.full_name : customName;

    if (!paidToReceivedFrom) {
      Alert.alert('Error', `Please select a ${type === 'expense' ? 'recipient' : 'sender'} or enter a custom name`);
      return;
    }

    if (isEditMode) {
      // Handle edit mode
      handleUpdateTransaction(paidToReceivedFrom);
    } else {
      // Handle create mode
      // Show confirmation dialog for linked transactions
      if (selectedProfile?.id) {
        Alert.alert(
          'Create Linked Transaction',
          `This will create a ${type === 'expense' ? 'income' : 'expense'} transaction for ${selectedProfile.full_name} as well. Continue?`,
          [
            {
              text: 'Cancel',
              style: 'cancel'
            },
            {
              text: 'Continue',
              onPress: () => saveTransactionWithLink(paidToReceivedFrom)
            }
          ]
        );
      } else {
        // No linked transaction needed
        saveTransactionWithLink(paidToReceivedFrom);
      }
    }
  };

  const handleUpdateTransaction = async (paidToReceivedFrom: string) => {
    if (!originalTransaction) {
      Alert.alert('Error', 'Original transaction data not found');
      return;
    }

    try {
      setSavingTransaction(true);

      const { data: userData, error: userError } = await supabase.auth.getUser();
      if (userError) throw userError;

      // Check if user owns this transaction
      if (originalTransaction.user_id !== userData.user?.id) {
        Alert.alert('Permission Denied', 'You can only edit transactions that you created.');
        return;
      }

      // Update the transaction
      const updatedTransaction = {
        type: type,
        amount: parseFloat(amount),
        description,
        transaction_date: new Date(dateStr).toISOString(),
        paid_to_received_from: paidToReceivedFrom,
        profile_id: selectedProfile?.id || null,
        counterparty_user_id: selectedProfile?.id || null,
        updated_at: new Date().toISOString()
      };

      const { error: updateError } = await supabase
        .from('transactions')
        .update(updatedTransaction)
        .eq('id', transactionId);

      if (updateError) {
        console.error('Error updating transaction:', updateError);
        Alert.alert('Error', 'Failed to update transaction. Please try again.');
        return;
      }

      // If there's a linked transaction, update it using the secure function
      if (originalTransaction.linked_transaction_id) {
        const { error: linkedUpdateError } = await supabase.rpc('update_linked_transactions', {
          p_original_transaction_id: transactionId,
          p_transaction_type: type,
          p_transaction_amount: parseFloat(amount),
          p_transaction_description: description,
          p_transaction_date: new Date(dateStr).toISOString()
        });

        if (linkedUpdateError) {
          console.error('Error updating linked transaction:', linkedUpdateError);
          // Continue even if linked transaction update fails
        }
      }

      Alert.alert('Success', 'Transaction updated successfully', [
        {
          text: 'OK',
          onPress: () => {
            // Navigate directly to the transaction detail page with refresh parameter
            router.push(`/transaction-detail?siteId=${siteId}&transactionId=${transactionId}&refresh=true`);
          }
        }
      ]);

    } catch (error) {
      console.error('Error updating transaction:', error);
      Alert.alert('Error', 'Failed to update transaction. Please try again.');
    } finally {
      setSavingTransaction(false);
    }
  };

  const saveTransactionWithLink = async (paidToReceivedFrom: string) => {
    try {
      setSavingTransaction(true);

      const { data: userData, error: userError } = await supabase.auth.getUser();
      if (userError) throw userError;

      // Get the current user's profile for reciprocal transaction if needed
      let currentUserProfile = null;
      if (selectedProfile?.id) {
        const { data: profile, error: profileError } = await supabase
          .from('profiles')
          .select('id, full_name, user_id')
          .eq('user_id', userData.user?.id)
          .single();
          
        if (!profileError) {
          currentUserProfile = profile;
        }
      }

      // Create the transaction for the current user with the selected type
      const currentUserTransaction = {
        type: type, // Using the selected type (income or expense)
        amount: parseFloat(amount),
        description,
        transaction_date: new Date(dateStr).toISOString(),
        paid_to_received_from: paidToReceivedFrom,
        profile_id: selectedProfile?.id || null,
        user_id: userData.user?.id,
        counterparty_user_id: selectedProfile?.id || null,
        site_id: siteId || null, // Add site_id if this is a site-specific transaction
        is_original: true // Mark this as the original transaction
      };

      console.log('Creating transaction:', JSON.stringify(currentUserTransaction));

      // Insert the current user's transaction
      const { data: currentTransactionData, error: currentUserError } = await supabase
        .from('transactions')
        .insert(currentUserTransaction)
        .select('id')
        .single();

      if (currentUserError) throw currentUserError;

      // If a profile was selected, create the linked transaction for them
      if (selectedProfile?.id && currentTransactionData?.id && currentUserProfile) {
        try {
          // Get the counterparty's user_id from their profile
          const { data: counterpartyProfile, error: counterpartyError } = await supabase
            .from('profiles')
            .select('user_id')
            .eq('id', selectedProfile.id)
            .single();
            
          if (counterpartyError || !counterpartyProfile?.user_id) {
            console.error("Error getting counterparty's user_id:", counterpartyError);
            Alert.alert(
              'Partial Success', 
              'Your transaction was saved, but we couldn\'t create the linked transaction for the other party.'
            );
            router.back();
            return;
          }
          
          // Create the linked transaction for the counterparty with the opposite type
          const otherUserTransaction = {
            type: type === 'expense' ? 'income' : 'expense', // Opposite type
            amount: parseFloat(amount),
            description,
            transaction_date: new Date(dateStr).toISOString(),
            paid_to_received_from: currentUserProfile.full_name || userData.user?.email || "Unknown user",
            profile_id: currentUserProfile.id || null,
            user_id: counterpartyProfile.user_id,
            counterparty_user_id: currentUserProfile.id || null,
            site_id: siteId || null, // Assign same site_id so linked transaction appears on the same site
            is_original: false // Mark this as a linked transaction, not original
            // No linked_transaction_id here, we'll update it separately
          };
  
          console.log('Creating linked transaction for user:', counterpartyProfile.user_id);
          console.log('Linked transaction data:', JSON.stringify(otherUserTransaction));
          
          const { data: otherData, error: otherUserError } = await supabase
            .from('transactions')
            .insert(otherUserTransaction)
            .select('id')
            .single();
  
          if (otherUserError) {
            console.error('Error creating linked transaction:', otherUserError);
            Alert.alert(
              'Partial Success', 
              'Your transaction was saved, but we couldn\'t create the linked transaction for the other party.'
            );
          } else {
            console.log('Linked transaction created successfully:', otherData);
            
            // Now update both transactions to reference each other using direct SQL to avoid the infinite recursion
            // This is a separate update that doesn't trigger the RLS policy recursion
            try {
              const { error: updateError } = await supabase.rpc('link_transactions_safely', {
                transaction1_id: currentTransactionData.id,
                transaction2_id: otherData.id
              });
              
              if (updateError) {
                console.error('Error linking transactions:', updateError);
              } else {
                console.log('Successfully linked transactions');
              }
            } catch (linkError) {
              console.error('Error in transaction linking:', linkError);
            }
            
            // Success with both transactions
            Alert.alert(
              'Success', 
              `Transaction saved successfully${selectedProfile?.id ? ' with a linked transaction for ' + selectedProfile.full_name : ''}`
            );
          }
        } catch (linkedError) {
          console.error('Error in linked transaction creation:', linkedError);
          Alert.alert(
            'Partial Success', 
            'Your transaction was saved, but we couldn\'t create the linked transaction for the other party.'
          );
        }
      } else {
        // Success with just the main transaction
        Alert.alert('Success', 'Transaction saved successfully');
      }
      
      router.back();
    } catch (error) {
      console.error('Error saving transaction:', error);
      console.error('Full error details:', JSON.stringify(error));
      Alert.alert('Error', 'Failed to save transaction. Please try again.');
    } finally {
      setSavingTransaction(false);
    }
  };

  return (
    <KeyboardAvoidingView
      style={{ flex: 1 }}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ThemedView style={[styles.container, { paddingTop: insets.top }]}>
        <View style={styles.header}>
          <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
            <Ionicons name="arrow-back" size={24} color={primaryColor} />
          </TouchableOpacity>
          <View style={styles.headerTitleContainer}>
            <ThemedText style={styles.headerTitle}>
              {isEditMode ? 'Edit Transaction' : 'Add Transaction'}
            </ThemedText>
            {site && (
              <ThemedText style={styles.siteIndicator}>
                for {site.name}
              </ThemedText>
            )}
          </View>
          <View style={{ width: 24 }} />
        </View>

        <ScrollView 
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.formContainer}
        >
          {/* Transaction Type Selector */}
          <View style={styles.fieldContainer}>
            <ThemedText style={styles.fieldLabel}>Transaction Type</ThemedText>
            <View style={styles.typeSelector}>
              <TouchableOpacity
                style={[
                  styles.typeButton,
                  type === 'expense' && { backgroundColor: '#f43f5e', borderColor: '#f43f5e' }
                ]}
                onPress={() => setType('expense')}
              >
                <MaterialIcons name="arrow-upward" size={18} color={type === 'expense' ? 'white' : '#f43f5e'} />
                <ThemedText style={[
                  styles.typeButtonText,
                  type === 'expense' && { color: 'white' }
                ]}>
                  Expense
                </ThemedText>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[
                  styles.typeButton,
                  type === 'income' && { backgroundColor: '#10b981', borderColor: '#10b981' }
                ]}
                onPress={() => setType('income')}
              >
                <MaterialIcons name="arrow-downward" size={18} color={type === 'income' ? 'white' : '#10b981'} />
                <ThemedText style={[
                  styles.typeButtonText,
                  type === 'income' && { color: 'white' }
                ]}>
                  Income
                </ThemedText>
              </TouchableOpacity>
            </View>
          </View>

          {/* Amount Field */}
          <View style={styles.fieldContainer}>
            <ThemedText style={styles.fieldLabel}>Amount (₹)</ThemedText>
            <View style={styles.inputWithIcon}>
              <ThemedText style={styles.currencySymbol}>₹</ThemedText>
              <TextInput
                style={styles.amountInput}
                value={amount}
                onChangeText={setAmount}
                placeholder="0.00"
                keyboardType="numeric"
                placeholderTextColor="#9ca3af"
              />
            </View>
          </View>

          {/* Description Field */}
          <View style={styles.fieldContainer}>
            <ThemedText style={styles.fieldLabel}>Description</ThemedText>
            <TextInput
              style={styles.input}
              value={description}
              onChangeText={setDescription}
              placeholder="Enter description"
              placeholderTextColor="#9ca3af"
            />
          </View>

          {/* Date Field */}
          <View style={styles.fieldContainer}>
            <ThemedText style={styles.fieldLabel}>Date (YYYY-MM-DD)</ThemedText>
            <View style={styles.dateInputContainer}>
              <TextInput
                style={styles.input}
                value={dateStr}
                onChangeText={setDateStr}
                placeholder="YYYY-MM-DD"
                placeholderTextColor="#9ca3af"
              />
              <ThemedText style={styles.dateFormatHint}>
                {formatDateDisplay(dateStr)}
              </ThemedText>
            </View>
          </View>

          {/* Paid To / Received From Field */}
          <View style={styles.fieldContainer}>
            <ThemedText style={styles.fieldLabel}>
              {type === 'expense' ? 'Paid To' : 'Received From'}
            </ThemedText>
            
            {selectedProfile ? (
              <View style={[
                styles.selectedProfileContainer,
                colorScheme === 'dark' && { backgroundColor: '#082f49', borderColor: '#0c4a6e' }
              ]}>
                <View style={styles.selectedProfileInfo}>
                  <ThemedText style={[
                    styles.selectedProfileName,
                    colorScheme === 'dark' && { color: '#FFFFFF' }
                  ]}>
                    {selectedProfile.full_name}
                  </ThemedText>
                  {selectedProfile.email && (
                    <ThemedText style={[
                      styles.selectedProfileDetail,
                      colorScheme === 'dark' && { color: '#9CA3AF' }
                    ]}>
                      {selectedProfile.email}
                    </ThemedText>
                  )}
                </View>
                <TouchableOpacity 
                  style={styles.clearButton}
                  onPress={() => setSelectedProfile(null)}
                >
                  <Ionicons name="close" size={20} color={colorScheme === 'dark' ? '#FFFFFF' : '#6b7280'} />
                </TouchableOpacity>
              </View>
            ) : (
              <>
                <TouchableOpacity 
                  style={styles.searchButton}
                  onPress={() => setShowProfileSearch(true)}
                >
                  <Ionicons name="search" size={20} color="#6b7280" />
                  <ThemedText style={styles.searchButtonText}>
                    Search from contacts
                  </ThemedText>
                </TouchableOpacity>
                
                <View style={styles.orContainer}>
                  <View style={styles.divider} />
                  <ThemedText style={styles.orText}>OR</ThemedText>
                  <View style={styles.divider} />
                </View>
                
                <TextInput
                  style={styles.input}
                  value={customName}
                  onChangeText={setCustomName}
                  placeholder="Enter custom name"
                  placeholderTextColor="#9ca3af"
                />
              </>
            )}
          </View>

          {/* Save Button */}
          <TouchableOpacity 
            style={[styles.saveButton, { backgroundColor: primaryColor }]}
            onPress={handleSave}
            disabled={savingTransaction}
          >
            {savingTransaction ? (
              <ActivityIndicator color="white" size="small" />
            ) : (
              <ThemedText style={styles.saveButtonText}>
                {isEditMode ? 'Update Transaction' : 'Save Transaction'}
              </ThemedText>
            )}
          </TouchableOpacity>
        </ScrollView>

        {/* Profile Search Modal */}
        <Modal
          visible={showProfileSearch}
          animationType="slide"
          transparent={true}
          onRequestClose={() => setShowProfileSearch(false)}
        >
          <View style={styles.modalContainer}>
            <ThemedView style={styles.modalContent}>
              <View style={styles.modalHeader}>
                <ThemedText style={styles.modalTitle}>
                  Select {type === 'expense' ? 'Recipient' : 'Sender'}
                </ThemedText>
                <TouchableOpacity 
                  onPress={() => setShowProfileSearch(false)}
                >
                  <Ionicons name="close" size={24} color={colorScheme === 'dark' ? '#d1d5db' : '#6b7280'} />
                </TouchableOpacity>
              </View>
              
              <View style={[
                styles.searchInputContainer, 
                { backgroundColor: colorScheme === 'dark' ? '#1F2937' : '#f9fafb' }
              ]}>
                <Ionicons name="search" size={20} color={colorScheme === 'dark' ? '#9ca3af' : '#6b7280'} style={styles.searchIcon} />
                <TextInput
                  style={[
                    styles.searchInput,
                    { color: colorScheme === 'dark' ? '#FFFFFF' : '#000000' }
                  ]}
                  value={searchQuery}
                  onChangeText={handleSearch}
                  placeholder="Search by name, email or phone"
                  placeholderTextColor={colorScheme === 'dark' ? '#9ca3af' : '#9ca3af'}
                  autoFocus
                />
                {searchQuery.length > 0 && (
                  <TouchableOpacity 
                    style={styles.clearSearchButton}
                    onPress={() => {
                      setSearchQuery('');
                      setFilteredProfiles([]);
                    }}
                  >
                    <Ionicons name="close-circle" size={20} color={colorScheme === 'dark' ? '#d1d5db' : '#6b7280'} />
                  </TouchableOpacity>
                )}
              </View>
              
              {loading ? (
                <View style={styles.loadingContainer}>
                  <ActivityIndicator size="large" color={primaryColor} />
                </View>
              ) : (
                <FlatList
                  data={filteredProfiles}
                  keyExtractor={item => item.id}
                  renderItem={({ item }) => (
                    <TouchableOpacity 
                      style={styles.profileItem}
                      onPress={() => selectProfile(item)}
                    >
                      <View style={[styles.profileAvatar, { backgroundColor: colorScheme === 'dark' ? '#374151' : '#e5e7eb' }]}>
                        <ThemedText style={[styles.avatarText, { color: primaryColor }]}>
                          {item.full_name.charAt(0).toUpperCase()}
                        </ThemedText>
                      </View>
                      <View style={styles.profileInfo}>
                        <ThemedText style={[
                          styles.profileName,
                          colorScheme === 'dark' && { color: '#FFFFFF' }
                        ]}>
                          {item.full_name}
                        </ThemedText>
                        {item.email && (
                          <ThemedText style={[
                            styles.profileDetail,
                            colorScheme === 'dark' && { color: '#9CA3AF' }
                          ]}>
                            {item.email}
                          </ThemedText>
                        )}
                        {item.phone_number && (
                          <ThemedText style={[
                            styles.profileDetail,
                            colorScheme === 'dark' && { color: '#9CA3AF' }
                          ]}>
                            {item.phone_number}
                          </ThemedText>
                        )}
                      </View>
                    </TouchableOpacity>
                  )}
                  ListEmptyComponent={
                    searchQuery.length > 0 ? (
                      <View style={styles.emptyContainer}>
                        <ThemedText style={styles.emptyText}>
                          No contacts found
                        </ThemedText>
                      </View>
                    ) : null
                  }
                />
              )}
            </ThemedView>
          </View>
        </Modal>
      </ThemedView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 24,
  },
  backButton: {
    padding: 4,
  },
  headerTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  siteIndicator: {
    fontSize: 14,
    color: '#6b7280',
    marginLeft: 8,
  },
  formContainer: {
    paddingBottom: 40,
  },
  fieldContainer: {
    marginBottom: 20,
  },
  fieldLabel: {
    fontSize: 16,
    marginBottom: 8,
    fontWeight: '500',
  },
  input: {
    backgroundColor: '#f9fafb',
    borderRadius: 8,
    padding: 12,
    borderWidth: 1,
    borderColor: '#e5e7eb',
    fontSize: 16,
  },
  inputWithIcon: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f9fafb',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e5e7eb',
  },
  currencySymbol: {
    fontSize: 18,
    marginLeft: 12,
    color: '#6b7280',
  },
  amountInput: {
    flex: 1,
    padding: 12,
    fontSize: 18,
    fontWeight: '600',
  },
  dateInputContainer: {
    marginBottom: 4,
  },
  dateFormatHint: {
    fontSize: 12,
    color: '#6b7280',
    marginTop: 4,
    marginLeft: 4,
  },
  typeSelector: {
    flexDirection: 'row',
    borderRadius: 8,
    overflow: 'hidden',
    marginTop: 4,
  },
  typeButton: {
    flex: 1,
    flexDirection: 'row',
    paddingVertical: 12,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: '#e5e7eb',
    marginHorizontal: 4,
    borderRadius: 8,
  },
  typeButtonText: {
    fontWeight: '500',
    marginLeft: 6,
  },
  searchButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f9fafb',
    borderRadius: 8,
    padding: 12,
    borderWidth: 1,
    borderColor: '#e5e7eb',
  },
  searchButtonText: {
    fontSize: 16,
    marginLeft: 8,
    color: '#6b7280',
  },
  orContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 12,
  },
  divider: {
    flex: 1,
    height: 1,
    backgroundColor: '#e5e7eb',
  },
  orText: {
    marginHorizontal: 8,
    color: '#6b7280',
    fontSize: 14,
  },
  selectedProfileContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#f0f9ff',
    borderRadius: 8,
    padding: 12,
    borderWidth: 1,
    borderColor: '#bae6fd',
  },
  selectedProfileInfo: {
    flex: 1,
  },
  selectedProfileName: {
    fontSize: 16,
    fontWeight: '500',
  },
  selectedProfileDetail: {
    fontSize: 14,
    color: '#6b7280',
    marginTop: 2,
  },
  clearButton: {
    padding: 4,
  },
  saveButton: {
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 20,
  },
  saveButtonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    padding: 16,
    height: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e5e7eb',
    marginBottom: 16,
  },
  searchIcon: {
    marginLeft: 12,
  },
  searchInput: {
    flex: 1,
    padding: 12,
    fontSize: 16,
  },
  clearSearchButton: {
    padding: 8,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  profileItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
    marginHorizontal: 2,
  },
  profileAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#e5e7eb',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  avatarText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#4b5563',
  },
  profileInfo: {
    flex: 1,
  },
  profileName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
  },
  profileDetail: {
    fontSize: 14,
    color: '#4b5563',
    marginTop: 2,
  },
  emptyContainer: {
    padding: 24,
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 16,
    color: '#6b7280',
    textAlign: 'center',
  },
}); 